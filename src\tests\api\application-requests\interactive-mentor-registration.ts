import { APIRequestContext } from '@playwright/test';
import RegisterAP<PERSON> from '../../../api/register/register.api';
import VerifyEmailAPI from '../../../api/verify-email/verify-email.api';
import ApplicationRequestsAPI from '../../../api/application-requests/application-requests.api';
import LoginLogoutAPI from '../../../api/login-logout/login-logout.api';
import { mapToRegisterRequest } from '../../../data-type/register-user-mapper.type';
import { CreateApplicationRequest } from '../../../data-type/application-request.type';
import testData from '../../../tests-data/application-requests-data.json';
import userRegisterData from '../../../tests-data/user-register-api-data.json';
import * as fs from 'fs';
import * as path from 'path';
import * as readline from 'readline';

interface RegistrationConfig {
    email: string;
    password: string;
    verificationCode?: string;
}

export class InteractiveMentorRegistration {
    private request: APIRequestContext;
    private registerAPI: RegisterAPI;
    private verifyEmailAPI: VerifyEmailAPI;
    private configPath: string;

    constructor(request: APIRequestContext) {
        this.request = request;
        this.registerAPI = new RegisterAPI(request);
        this.verifyEmailAPI = new VerifyEmailAPI(request);
        this.configPath = path.join(__dirname, 'registration-config.json');
    }

    private loadRegistrationConfig(): RegistrationConfig | null {
        try {
            if (fs.existsSync(this.configPath)) {
                const configData = fs.readFileSync(this.configPath, 'utf8');
                return JSON.parse(configData);
            }
        } catch (error) {
            console.warn('Failed to load registration config:', error.message);
        }
        return null;
    }

    private saveRegistrationConfig(config: RegistrationConfig): void {
        try {
            fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
        } catch (error) {
            console.warn('Failed to save registration config:', error.message);
        }
    }

    private generateTestEmail(): string {
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        return `mentor.test.${timestamp}.${randomSuffix}@10minutemail.com`;
    }

    private generateTestPassword(): string {
        return 'TestMentor123!@#';
    }

    async registerMentorInteractively(): Promise<InteractiveRegistrationResult> {
        try {
            console.log('\n=== Automated Mentor Registration for API Testing ===');

            // Check if we have a saved config
            let config = this.loadRegistrationConfig();

            if (!config) {
                // Generate test credentials
                config = {
                    email: this.generateTestEmail(),
                    password: this.generateTestPassword()
                };
                this.saveRegistrationConfig(config);
                console.log('📝 Generated new test credentials');
            } else {
                console.log('📂 Using saved test credentials');
            }

            console.log('📧 Email:', config.email);
            console.log('🔒 Password: [HIDDEN]');

            // Step 1: Prepare registration data
            const registrationData: MentorRegistrationData = {
                ...testData.mentorRegistrationData,
                email: config.email,
                password: config.password
            };

            console.log('\n⏳ Registering mentor account...');

            // Step 2: Register the mentor
            const mappedData = mapToRegisterRequest(registrationData);
            const registerResponse = await this.registerAPI.register(mappedData);

            if (!registerResponse.ok()) {
                const errorText = await registerResponse.text();

                // If email already exists, try to use existing account
                if (errorText.includes('Email already register')) {
                    console.log('📧 Email already registered, attempting to use existing account...');
                    return await this.handleExistingAccount(config);
                }

                throw new Error(`Registration failed: ${errorText}`);
            }

            console.log('✅ Registration successful!');

            // Step 3: Handle email verification
            return await this.handleEmailVerification(config);

        } catch (error) {
            console.error('\n❌ Registration failed:', error.message);
            throw error;
        }
    }

    private async handleExistingAccount(config: RegistrationConfig): Promise<InteractiveRegistrationResult> {
        // For existing accounts, we'll need to implement login logic
        // For now, we'll throw an error and suggest manual cleanup
        throw new Error(`Account with email ${config.email} already exists. Please delete the registration-config.json file and try again.`);
    }

    private async handleEmailVerification(config: RegistrationConfig): Promise<InteractiveRegistrationResult> {
        console.log('\n📧 Email verification required');
        console.log('⚠️  MANUAL STEP REQUIRED:');
        console.log(`1. Check email: ${config.email}`);
        console.log('2. Find the 6-digit verification code');
        console.log('3. Update the registration-config.json file with the verification code');
        console.log('4. Re-run the test');

        // Check if verification code is already provided
        if (config.verificationCode) {
            console.log('\n⏳ Using provided verification code...');

            const verifyResponse = await this.verifyEmailAPI.verifyEmail({
                email: config.email,
                code: config.verificationCode
            });

            if (!verifyResponse.ok()) {
                const errorText = await verifyResponse.text();
                throw new Error(`Email verification failed: ${errorText}`);
            }

            const verifyResponseBody = await verifyResponse.json();

            console.log('✅ Email verification successful!');
            console.log('🎉 Mentor account is ready for API testing');

            return {
                email: config.email,
                password: config.password,
                accessToken: verifyResponseBody.accessToken,
                refreshToken: verifyResponseBody.refreshToken
            };
        } else {
            // Save current config and throw error with instructions
            this.saveRegistrationConfig(config);
            throw new Error(`Please check email ${config.email} for verification code and add it to registration-config.json as "verificationCode": "123456"`);
        }
    }

    async cleanup(): Promise<void> {
        // Clean up config file if needed
        try {
            if (fs.existsSync(this.configPath)) {
                fs.unlinkSync(this.configPath);
                console.log('🧹 Cleaned up registration config');
            }
        } catch (error) {
            console.warn('Failed to cleanup config:', error.message);
        }
    }
}
