{"createRequest": {"validData": {"Education": "Master's in Computer Science", "WorkExperience": "5 years as Senior Developer", "Certifications": ["AWS Certified", "Scrum Master"], "Description": "Experienced developer looking to mentor junior developers"}, "validDataWithStatus": {"Education": "PhD in Software Engineering", "WorkExperience": "8 years as Tech Lead", "Certifications": ["Google Cloud Professional", "Azure Solutions Architect"], "Description": "Senior developer with leadership experience", "Status": 1}, "validDataMinimal": {"Education": "Bachelor's in Computer Science", "WorkExperience": "2 years as Junior Developer", "Certifications": ["Basic Programming"], "Description": "Junior developer eager to mentor"}, "invalidData": {"emptyEducation": {"Education": "", "WorkExperience": "5 years experience", "Certifications": ["AWS"], "Description": "Test description", "expectedStatus": 400, "expectedMessage": "Education is required"}, "emptyWorkExperience": {"Education": "Master's degree", "WorkExperience": "", "Certifications": ["AWS"], "Description": "Test description", "expectedStatus": 400, "expectedMessage": "Work experience is required"}, "emptyDescription": {"Education": "Master's degree", "WorkExperience": "5 years experience", "Certifications": ["AWS"], "Description": "", "expectedStatus": 400, "expectedMessage": "Description is required"}, "emptyCertifications": {"Education": "Master's degree", "WorkExperience": "5 years experience", "Certifications": [], "Description": "Test description", "expectedStatus": 400, "expectedMessage": "At least one certification is required"}}, "expectedResponseFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"], "expectedSuccessMessage": "Create successfully"}, "updateRequest": {"validData": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Updated Master's in Computer Science with AI specialization", "WorkExperience": "Updated: 7 years as Senior Developer and Team Lead", "Certifications": ["Updated AWS Certified Solutions Architect", "Updated Scrum Master", "New Azure Certification"], "Description": "Updated description: Experienced developer with leadership skills looking to mentor junior developers"}, "validDataMinimal": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Updated Bachelor's degree", "WorkExperience": "Updated 3 years experience", "Certifications": ["Updated Basic Certification"], "Description": "Updated minimal description"}, "invalidData": {"notFound": {"Id": "00000000-0000-0000-0000-000000000000", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "Application request not found"}, "invalidId": {"Id": "invalid-uuid-format", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "Invalid ID format"}, "emptyId": {"Id": "", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "ID is required"}, "emptyEducation": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "Education is required"}, "emptyWorkExperience": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Education", "WorkExperience": "", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "Work experience is required"}, "emptyDescription": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "", "expectedStatus": 400, "expectedMessage": "Description is required"}, "emptyCertifications": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Education", "WorkExperience": "Experience", "Certifications": [], "Description": "Description", "expectedStatus": 400, "expectedMessage": "At least one certification is required"}}, "expectedResponseFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"], "expectedSuccessMessage": "Update successfully", "expectedErrorMessages": {"notFound": "Application request not found", "notUnderReview": "<PERSON><PERSON> cannot update request that is not under review"}}, "getRequests": {"validParams": {"basic": {"PageSize": 10, "PageNumber": 1}, "withSearch": {"PageSize": 5, "PageNumber": 1, "Search": "developer"}, "withStatusFilter": {"PageSize": 10, "PageNumber": 1, "ApplicationRequestStatuses": [1, 2]}, "withAllFilters": {"PageSize": 15, "PageNumber": 2, "Search": "mentor", "ApplicationRequestStatuses": [1, 2, 3]}, "largePageSize": {"PageSize": 50, "PageNumber": 1}, "specificPage": {"PageSize": 5, "PageNumber": 3}}, "invalidParams": {"negativePageSize": {"PageSize": -1, "PageNumber": 1, "expectedStatus": 400, "expectedMessage": "PageSize must be greater than 0"}, "zeroPageSize": {"PageSize": 0, "PageNumber": 1, "expectedStatus": 400, "expectedMessage": "PageSize must be greater than 0"}, "negativePageNumber": {"PageSize": 10, "PageNumber": -1, "expectedStatus": 400, "expectedMessage": "PageNumber must be greater than 0"}, "zeroPageNumber": {"PageSize": 10, "PageNumber": 0, "expectedStatus": 400, "expectedMessage": "PageNumber must be greater than 0"}, "tooLargePageSize": {"PageSize": 1000, "PageNumber": 1, "expectedStatus": 400, "expectedMessage": "PageSize cannot exceed maximum limit"}}, "expectedResponseFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"], "expectedStatuses": {"pending": 1, "approved": 2, "rejected": 3, "underReview": 4}, "paginationFields": ["pageNumber", "pageSize", "totalCount", "totalPages", "hasPreviousPage", "hasNextPage"]}, "requestUpdate": {"validData": {"note": "Please provide more details about your experience"}}, "rejectRequest": {"validData": {"note": "Application does not meet our requirements"}}, "testIds": {"validId": "123e4567-e89b-12d3-a456-************", "invalidId": "00000000-0000-0000-0000-000000000000"}, "getRequestById": {"expectedDetailFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted", "note", "applicationRequestDocuments", "mentorEmail", "mentorExpertises", "mentorCertifications", "avatarUrl"], "expectedDocumentFields": ["filePath", "fileName"], "validDetailResponse": {"id": "123e4567-e89b-12d3-a456-************", "education": "Master's in Computer Science from Stanford University", "workExperience": "5 years as Senior Software Developer at Google", "fullName": "<PERSON>", "description": "Experienced developer passionate about mentoring", "status": 1, "summitted": "2024-01-15T10:30:00Z", "note": "Application looks good, please provide more details about leadership experience", "applicationRequestDocuments": [{"filePath": "/documents/resume.pdf", "fileName": "resume.pdf"}, {"filePath": "/documents/certificate.pdf", "fileName": "aws_certificate.pdf"}], "mentorEmail": "<EMAIL>", "mentorExpertises": [1, 2, 3], "mentorCertifications": "AWS Certified Solutions Architect, Scrum Master", "avatarUrl": "https://example.com/avatar.jpg"}, "invalidIdFormats": [{"id": "invalid-uuid", "expectedStatus": 400, "expectedMessage": "Invalid ID format"}, {"id": "123", "expectedStatus": 400, "expectedMessage": "Invalid ID format"}, {"id": "", "expectedStatus": 400, "expectedMessage": "ID is required"}], "notFoundId": {"id": "*************-9999-9999-************", "expectedStatus": 404, "expectedMessage": "Application request not found"}}, "getCurrentUserRequest": {"expectedFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"], "expectedStatuses": {"pending": 1, "approved": 2, "rejected": 3, "requestUpdate": 4}, "validResponse": {"id": "123e4567-e89b-12d3-a456-************", "education": "Master's in Computer Science", "workExperience": "5 years as Senior Developer", "fullName": "Test Mentor", "description": "Experienced developer looking to mentor junior developers", "status": 1, "summitted": "2024-01-15T10:30:00Z"}, "noApplicationResponse": {"message": "No application request found for current user"}}}