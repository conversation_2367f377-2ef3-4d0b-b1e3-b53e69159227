{"createRequest": {"validData": {"Education": "Master's in Computer Science", "WorkExperience": "5 years as Senior Developer", "Certifications": ["AWS Certified", "Scrum Master"], "Description": "Experienced developer looking to mentor junior developers"}, "validDataWithStatus": {"Education": "PhD in Software Engineering", "WorkExperience": "8 years as Tech Lead", "Certifications": ["Google Cloud Professional", "Azure Solutions Architect"], "Description": "Senior developer with leadership experience", "Status": 1}, "validDataMinimal": {"Education": "Bachelor's in Computer Science", "WorkExperience": "2 years as Junior Developer", "Certifications": ["Basic Programming"], "Description": "Junior developer eager to mentor"}, "invalidData": {"emptyEducation": {"Education": "", "WorkExperience": "5 years experience", "Certifications": ["AWS"], "Description": "Test description", "expectedStatus": 400, "expectedMessage": "Education is required"}, "emptyWorkExperience": {"Education": "Master's degree", "WorkExperience": "", "Certifications": ["AWS"], "Description": "Test description", "expectedStatus": 400, "expectedMessage": "Work experience is required"}, "emptyDescription": {"Education": "Master's degree", "WorkExperience": "5 years experience", "Certifications": ["AWS"], "Description": "", "expectedStatus": 400, "expectedMessage": "Description is required"}, "emptyCertifications": {"Education": "Master's degree", "WorkExperience": "5 years experience", "Certifications": [], "Description": "Test description", "expectedStatus": 400, "expectedMessage": "At least one certification is required"}}, "expectedResponseFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"], "expectedSuccessMessage": "Create successfully"}, "updateRequest": {"validData": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Updated Master's in Computer Science with AI specialization", "WorkExperience": "Updated: 7 years as Senior Developer and Team Lead", "Certifications": ["Updated AWS Certified Solutions Architect", "Updated Scrum Master", "New Azure Certification"], "Description": "Updated description: Experienced developer with leadership skills looking to mentor junior developers"}, "validDataMinimal": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Updated Bachelor's degree", "WorkExperience": "Updated 3 years experience", "Certifications": ["Updated Basic Certification"], "Description": "Updated minimal description"}, "invalidData": {"notFound": {"Id": "00000000-0000-0000-0000-000000000000", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "Application request not found"}, "invalidId": {"Id": "invalid-uuid-format", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "Invalid ID format"}, "emptyId": {"Id": "", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "ID is required"}, "emptyEducation": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "Education is required"}, "emptyWorkExperience": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Education", "WorkExperience": "", "Certifications": ["Cert"], "Description": "Description", "expectedStatus": 400, "expectedMessage": "Work experience is required"}, "emptyDescription": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "", "expectedStatus": 400, "expectedMessage": "Description is required"}, "emptyCertifications": {"Id": "123e4567-e89b-12d3-a456-************", "Education": "Education", "WorkExperience": "Experience", "Certifications": [], "Description": "Description", "expectedStatus": 400, "expectedMessage": "At least one certification is required"}}, "expectedResponseFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"], "expectedSuccessMessage": "Update successfully", "expectedErrorMessages": {"notFound": "Application request not found", "notUnderReview": "<PERSON><PERSON> cannot update request that is not under review"}}, "getRequests": {"validParams": {"basic": {"PageSize": 10, "PageNumber": 1}, "withSearch": {"PageSize": 5, "PageNumber": 1, "Search": "developer"}, "withStatusFilter": {"PageSize": 10, "PageNumber": 1, "ApplicationRequestStatuses": [1, 2]}, "withAllFilters": {"PageSize": 15, "PageNumber": 2, "Search": "mentor", "ApplicationRequestStatuses": [1, 2, 3]}, "largePageSize": {"PageSize": 50, "PageNumber": 1}, "specificPage": {"PageSize": 5, "PageNumber": 3}}, "invalidParams": {"negativePageSize": {"PageSize": -1, "PageNumber": 1, "expectedStatus": 400, "expectedMessage": "PageSize must be greater than 0"}, "zeroPageSize": {"PageSize": 0, "PageNumber": 1, "expectedStatus": 400, "expectedMessage": "PageSize must be greater than 0"}, "negativePageNumber": {"PageSize": 10, "PageNumber": -1, "expectedStatus": 400, "expectedMessage": "PageNumber must be greater than 0"}, "zeroPageNumber": {"PageSize": 10, "PageNumber": 0, "expectedStatus": 400, "expectedMessage": "PageNumber must be greater than 0"}, "tooLargePageSize": {"PageSize": 1000, "PageNumber": 1, "expectedStatus": 400, "expectedMessage": "PageSize cannot exceed maximum limit"}}, "expectedResponseFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"], "expectedStatuses": {"pending": 1, "approved": 2, "rejected": 3, "underReview": 4}, "paginationFields": ["pageNumber", "pageSize", "totalCount", "totalPages", "hasPreviousPage", "hasNextPage"]}, "requestUpdate": {"validData": {"note": "Please provide more details about your experience"}}, "rejectRequest": {"validData": {"note": "Application does not meet our requirements. Please improve your experience and reapply."}, "validDataDetailed": {"note": "After careful review, we found that your application lacks sufficient experience in the required areas. We recommend gaining more hands-on experience and reapplying in the future."}, "validDataShort": {"note": "Insufficient experience."}, "invalidData": {"emptyNote": {"note": "", "expectedStatus": 400, "expectedMessage": "Note is required"}, "missingNote": {"expectedStatus": 400, "expectedMessage": "Note is required"}}, "businessRuleErrors": {"notFound": {"id": "*************-9999-9999-************", "note": "Valid rejection note", "expectedStatus": 404, "expectedMessage": "Application request not found"}, "underReview": {"note": "Cannot reject under review application", "expectedStatus": 400, "expectedMessage": "Admin cannot reject request that is under review"}, "alreadyApproved": {"note": "Cannot reject approved application", "expectedStatus": 400, "expectedMessage": "Cannot reject approved request"}}, "testIds": {"validForRejection": "88100006-db45-4b9f-e1cf-08dd9e61b811", "underReviewId": "a74b2365-14dd-4c18-f003-08dd9dcaf40f", "approvedId": "2100f9d3-8271-4c3e-c7c2-08dd9da05f0d"}, "expectedSuccessResponse": {"isSuccess": true, "statusCode": 200, "errors": []}}, "testIds": {"validId": "123e4567-e89b-12d3-a456-************", "invalidId": "00000000-0000-0000-0000-000000000000"}, "getRequestById": {"expectedDetailFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted", "note", "applicationRequestDocuments", "mentorEmail", "mentorExpertises", "mentorCertifications", "avatarUrl"], "expectedDocumentFields": ["filePath", "fileName"], "validDetailResponse": {"id": "a74b2365-14dd-4c18-f003-08dd9dcaf40f", "education": "MIT University", "workExperience": "10 year at Google", "fullName": "<PERSON><PERSON><PERSON>", "description": "10 year at Google", "status": 1, "summitted": "2025-05-28T09:35:14.101145", "note": "You need to update bio", "applicationRequestDocuments": [{"filePath": "https://res.cloudinary.com/du3a3d1dh/image/upload/v1748424915/mentor-platform/images/images_m3g8qy.jpg", "fileName": "images.jpg"}], "mentorEmail": "<EMAIL>", "mentorExpertises": ["Leadership", "Design"], "mentorCertifications": ["MIT University", "MIT University"], "avatarUrl": ""}, "invalidIdFormats": [{"id": "invalid-uuid", "expectedStatus": 400, "expectedMessage": "Invalid ID format"}, {"id": "123", "expectedStatus": 400, "expectedMessage": "Invalid ID format"}, {"id": "", "expectedStatus": 400, "expectedMessage": "ID is required"}], "notFoundId": {"id": "*************-9999-9999-************", "expectedStatus": 404, "expectedMessage": "Application request not found"}}, "getCurrentUserRequest": {"expectedFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"], "expectedStatuses": {"pending": 1, "approved": 2, "rejected": 3, "requestUpdate": 4}, "validResponse": {"id": "123e4567-e89b-12d3-a456-************", "education": "Master's in Computer Science", "workExperience": "5 years as Senior Developer", "fullName": "Test Mentor", "description": "Experienced developer looking to mentor junior developers", "status": 1, "summitted": "2024-01-15T10:30:00Z"}, "noApplicationResponse": {"message": "No application request found for current user"}}}