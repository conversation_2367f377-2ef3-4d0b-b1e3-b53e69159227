{"createRequest": {"validData": {"Education": "Master's in Computer Science", "WorkExperience": "5 years as Senior Developer", "Certifications": ["AWS Certified", "Scrum Master"], "Description": "Experienced developer looking to mentor junior developers"}, "invalidData": {"emptyEducation": {"Education": "", "WorkExperience": "5 years experience", "Certifications": ["AWS"], "Description": "Test description"}}}, "updateRequest": {"validData": {"Id": "123e4567-e89b-12d3-a456-426614174000", "Education": "Updated education", "WorkExperience": "Updated experience", "Certifications": ["Updated certification"], "Description": "Updated description"}, "invalidData": {"notFound": {"Id": "00000000-0000-0000-0000-000000000000", "Education": "Education", "WorkExperience": "Experience", "Certifications": ["Cert"], "Description": "Description"}}}, "getRequests": {"validParams": {"PageSize": 10, "PageNumber": 1, "Search": "test", "ApplicationRequestStatuses": [1, 2]}}, "requestUpdate": {"validData": {"note": "Please provide more details about your experience"}}, "rejectRequest": {"validData": {"note": "Application does not meet our requirements"}}, "testIds": {"validId": "123e4567-e89b-12d3-a456-426614174000", "invalidId": "00000000-0000-0000-0000-000000000000"}}