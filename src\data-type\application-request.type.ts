export interface ApplicationDocument {
    filePath: string;
    fileName: string;
    fileContent?: string;
}

export interface CreateApplicationRequest {
    Education: string;
    WorkExperience: string;
    Certifications: string[];
    Description: string;
    Status?: number;
    ApplicationDocuments?: File[];
}

export interface UpdateApplicationRequest {
    Id: string;
    Education: string;
    WorkExperience: string;
    Certifications: string[];
    Description: string;
    ApplicationDocuments?: ApplicationDocument[];
}

export interface ApplicationRequestResponse {
    id: string;
    education: string;
    workExperience: string;
    fullName: string;
    description: string;
    status: number;
    summitted: string;
}

export interface ApplicationRequestDetailResponse extends ApplicationRequestResponse {
    note: string;
    applicationRequestDocuments: ApplicationDocument[];
    mentorEmail: string;
    mentorExpertises: number[];
    mentorCertifications: string;
    avatarUrl: string;
}

export interface GetApplicationRequestsParams {
    PageSize: number;
    PageNumber: number;
    Search?: string;
    ApplicationRequestStatuses?: number[];
}

export interface RequestUpdateBody {
    note: string;
}

export interface RejectRequestBody {
    note: string;
}

export interface CurrentUserApplicationRequestResponse {
    id: string;
    education: string;
    workExperience: string;
    fullName: string;
    description: string;
    status: number;
    summitted: string;
}

export interface NoApplicationRequestResponse {
    message: string;
}