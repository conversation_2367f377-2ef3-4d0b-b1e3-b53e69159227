import { expect } from "@playwright/test";
import { test } from "@core/fixture/fixture-api";
import testData from "@tests-data/application-requests-data.json";
import ApplicationRequestsAPI from "src/api/application-requests/application-requests.api";
import LoginLogoutAPI from "src/api/login-logout/login-logout.api";
import {
    CurrentUserApplicationRequestResponse,
    NoApplicationRequestResponse,
    CreateApplicationRequest,
    UpdateApplicationRequest,
    ApplicationRequestResponse,
    ApplicationRequestDetailResponse,
    GetApplicationRequestsParams,
    RejectRequestBody
} from "src/data-type/application-request.type";
import { ApiResponseType } from "src/data-type/response.type";

test.describe('Application Requests API - Current User', () => {
    let applicationRequestsAPI: ApplicationRequestsAPI;

    test.beforeEach(async ({ request }) => {
        applicationRequestsAPI = new ApplicationRequestsAPI(request);
    });

    test('Verify get current user application request API successfully', async () => {
        let response: any;
        let responseBody: ApiResponseType;

        await test.step('Send GET request to current user application request endpoint', async () => {
            response = await applicationRequestsAPI.getCurrentUserApplicationRequest();
            responseBody = await response.json();
        });

        await test.step('Verify successful response', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);
        });

        await test.step('Verify response data structure when application exists', async () => {
            if (responseBody.data && responseBody.data.id) {
                const applicationData = responseBody.data as CurrentUserApplicationRequestResponse;

                // Verify all expected fields are present
                testData.getCurrentUserRequest.expectedFields.forEach(field => {
                    expect(applicationData).toHaveProperty(field);
                });

                // Verify data types
                expect(typeof applicationData.id).toBe('string');
                expect(typeof applicationData.education).toBe('string');
                expect(typeof applicationData.workExperience).toBe('string');
                expect(typeof applicationData.fullName).toBe('string');
                expect(typeof applicationData.description).toBe('string');
                expect(typeof applicationData.status).toBe('number');
                expect(typeof applicationData.summitted).toBe('string');

                // Verify status is a valid number (adjust based on actual API response)
                expect(typeof applicationData.status).toBe('number');
                expect(applicationData.status).toBeGreaterThanOrEqual(0);
            }
        });
    });

    test('Verify get current user application request API when no application exists', async () => {
        let response: any;
        let responseBody: ApiResponseType;

        await test.step('Send GET request to current user application request endpoint', async () => {
            response = await applicationRequestsAPI.getCurrentUserApplicationRequest();
            responseBody = await response.json();
        });

        await test.step('Verify response when no application exists', async () => {
            // This test assumes the API returns 404 or 200 with null data when no application exists
            // Adjust based on actual API behavior
            if (response.status() === 404) {
                expect(response.status()).toBe(404);
            } else if (response.status() === 200) {
                expect(responseBody.statusCode).toBe(200);
                // Data might be null or contain a message
                if (responseBody.data === null || responseBody.data === undefined) {
                    expect(responseBody.data).toBeNull();
                } else if (responseBody.data && responseBody.data.message) {
                    const noAppData = responseBody.data as NoApplicationRequestResponse;
                    expect(noAppData.message).toBeDefined();
                    expect(typeof noAppData.message).toBe('string');
                }
            }
        });
    });

    test('Verify get current user application request API response structure', async () => {
        let response: any;
        let responseBody: ApiResponseType;

        await test.step('Send GET request to current user application request endpoint', async () => {
            response = await applicationRequestsAPI.getCurrentUserApplicationRequest();
            responseBody = await response.json();
        });

        await test.step('Verify API response structure', async () => {
            expect(response.status()).toBe(200);

            // Verify standard API response structure
            expect(responseBody).toHaveProperty('statusCode');
            expect(responseBody).toHaveProperty('isSuccess');
            expect(responseBody).toHaveProperty('data');
            expect(responseBody).toHaveProperty('errors');

            expect(typeof responseBody.statusCode).toBe('number');
            expect(typeof responseBody.isSuccess).toBe('boolean');
            expect(Array.isArray(responseBody.errors)).toBe(true);
        });
    });
});

test.describe('Application Requests API - Create Request', () => {
    let applicationRequestsAPI: ApplicationRequestsAPI;

    test.beforeEach(async ({ request }) => {
        applicationRequestsAPI = new ApplicationRequestsAPI(request);
    });

    test('Verify create application request API with valid data successfully', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestData = testData.createRequest.validData as CreateApplicationRequest;

        await test.step('Send POST request to create application request', async () => {
            response = await applicationRequestsAPI.createApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify response based on user state', async () => {
            if (response.status() === 200) {
                // User doesn't have existing application - successful creation
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);

                if (responseBody.data) {
                    const applicationData = responseBody.data as ApplicationRequestResponse;

                    // Verify all expected fields are present
                    testData.createRequest.expectedResponseFields.forEach(field => {
                        expect(applicationData).toHaveProperty(field);
                    });

                    // Verify data types
                    expect(typeof applicationData.id).toBe('string');
                    expect(typeof applicationData.education).toBe('string');
                    expect(typeof applicationData.workExperience).toBe('string');
                    expect(typeof applicationData.fullName).toBe('string');
                    expect(typeof applicationData.description).toBe('string');
                    expect(typeof applicationData.status).toBe('number');
                    expect(typeof applicationData.summitted).toBe('string');

                    // Verify the data matches what was sent
                    expect(applicationData.education).toBe(requestData.Education);
                    expect(applicationData.workExperience).toBe(requestData.WorkExperience);
                    expect(applicationData.description).toBe(requestData.Description);
                }
            } else if (response.status() === 400) {
                // User already has pending application - expected business rule
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors).toBeDefined();
                expect(responseBody.errors.length).toBeGreaterThan(0);
                expect(responseBody.errors[0].code).toBe('UserAlreadyHasPendingApplication');
                expect(responseBody.errors[0].message).toContain('already have an application');
            } else {
                throw new Error(`Unexpected response status: ${response.status()}`);
            }
        });
    });

    test('Verify create application request API with status field', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestData = testData.createRequest.validDataWithStatus as CreateApplicationRequest;

        await test.step('Send POST request with status field', async () => {
            response = await applicationRequestsAPI.createApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify response based on user state', async () => {
            if (response.status() === 200) {
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);

                if (responseBody.data) {
                    const applicationData = responseBody.data as ApplicationRequestResponse;
                    expect(applicationData.status).toBe(requestData.Status);
                }
            } else if (response.status() === 400) {
                // User already has pending application
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors[0].code).toBe('UserAlreadyHasPendingApplication');
            }
        });
    });

    test('Verify create application request API with minimal data', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestData = testData.createRequest.validDataMinimal as CreateApplicationRequest;

        await test.step('Send POST request with minimal required data', async () => {
            response = await applicationRequestsAPI.createApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify response based on user state', async () => {
            if (response.status() === 200) {
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
            } else if (response.status() === 400) {
                // User already has pending application
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors[0].code).toBe('UserAlreadyHasPendingApplication');
            }
        });
    });

    // Negative test cases
    Object.entries(testData.createRequest.invalidData).forEach(([testCase, invalidData]) => {
        test(`Verify create application request API validation - ${testCase}`, async () => {
            let response: any;
            let responseBody: ApiResponseType;
            const requestData = invalidData as CreateApplicationRequest & { expectedStatus: number; expectedMessage: string };

            await test.step(`Send POST request with ${testCase}`, async () => {
                response = await applicationRequestsAPI.createApplicationRequest(requestData);
                responseBody = await response.json();
            });

            await test.step('Verify validation error response', async () => {
                expect(response.status()).toBe(requestData.expectedStatus);
                expect(responseBody.statusCode).toBe(requestData.expectedStatus);
                expect(responseBody.isSuccess).toBe(false);

                // Check if error message is present
                if (responseBody.errors && responseBody.errors.length > 0) {
                    expect(responseBody.errors[0]).toHaveProperty('message');
                    expect(typeof responseBody.errors[0].message).toBe('string');
                }
            });
        });
    });

    test('Verify create application request API response structure', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestData = testData.createRequest.validData as CreateApplicationRequest;

        await test.step('Send POST request to create application request', async () => {
            response = await applicationRequestsAPI.createApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify API response structure', async () => {
            // Verify standard API response structure
            expect(responseBody).toHaveProperty('statusCode');
            expect(responseBody).toHaveProperty('isSuccess');
            expect(responseBody).toHaveProperty('errors');

            expect(typeof responseBody.statusCode).toBe('number');
            expect(typeof responseBody.isSuccess).toBe('boolean');
            expect(Array.isArray(responseBody.errors)).toBe(true);

            // Data field is only present on successful responses
            if (response.status() === 200) {
                expect(responseBody).toHaveProperty('data');
            }
        });
    });

    test('Verify create application request API - duplicate application prevention', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestData = testData.createRequest.validData as CreateApplicationRequest;

        await test.step('Send POST request to create application request', async () => {
            response = await applicationRequestsAPI.createApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify duplicate application prevention', async () => {
            if (response.status() === 400) {
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors).toBeDefined();
                expect(responseBody.errors.length).toBeGreaterThan(0);
                expect(responseBody.errors[0].code).toBe('UserAlreadyHasPendingApplication');
                expect(responseBody.errors[0].message).toContain('already have an application');

                console.log('✓ API correctly prevents duplicate applications');
            } else if (response.status() === 200) {
                console.log('✓ User can create new application (no existing pending application)');
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
            }
        });
    });
});

test.describe('Application Requests API - Update Request', () => {
    let applicationRequestsAPI: ApplicationRequestsAPI;
    let currentUserApplicationId: string;

    test.beforeEach(async ({ request }) => {
        applicationRequestsAPI = new ApplicationRequestsAPI(request);

        // Get current user's application ID for update tests
        const getCurrentResponse = await applicationRequestsAPI.getCurrentUserApplicationRequest();
        const getCurrentBody = await getCurrentResponse.json();

        if (getCurrentResponse.status() === 200 && getCurrentBody.data && getCurrentBody.data.id) {
            currentUserApplicationId = getCurrentBody.data.id;
        }
    });

    test('Verify update application request API with valid data successfully', async () => {
        let response: any;
        let responseBody: ApiResponseType;

        await test.step('Prepare update request data', async () => {
            // Skip test if no current application exists
            if (!currentUserApplicationId) {
                test.skip();
            }
        });

        const requestData: UpdateApplicationRequest = {
            ...testData.updateRequest.validData,
            Id: currentUserApplicationId
        } as UpdateApplicationRequest;

        await test.step('Send PUT request to update application request', async () => {
            response = await applicationRequestsAPI.updateApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify response based on application state', async () => {
            if (response.status() === 200) {
                // Application is in updatable state (under review)
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);

                if (responseBody.data) {
                    const applicationData = responseBody.data as ApplicationRequestResponse;

                    // Verify all expected fields are present
                    testData.updateRequest.expectedResponseFields.forEach(field => {
                        expect(applicationData).toHaveProperty(field);
                    });

                    // Verify the data was updated
                    expect(applicationData.education).toBe(requestData.Education);
                    expect(applicationData.workExperience).toBe(requestData.WorkExperience);
                    expect(applicationData.description).toBe(requestData.Description);
                    expect(applicationData.id).toBe(requestData.Id);
                }
            } else if (response.status() === 400) {
                // Application is not in updatable state
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors).toBeDefined();
                expect(responseBody.errors.length).toBeGreaterThan(0);

                // Check for specific error message about update restrictions
                const errorMessage = responseBody.errors[0].message;
                expect(errorMessage).toContain('can not update request');
            } else if (response.status() === 404) {
                // Application not found
                expect(responseBody.statusCode).toBe(404);
                expect(responseBody.isSuccess).toBe(false);
            }
        });
    });

    test('Verify update application request API with minimal data', async () => {
        let response: any;
        let responseBody: ApiResponseType;

        await test.step('Prepare minimal update request data', async () => {
            if (!currentUserApplicationId) {
                test.skip();
            }
        });

        const requestData: UpdateApplicationRequest = {
            ...testData.updateRequest.validDataMinimal,
            Id: currentUserApplicationId
        } as UpdateApplicationRequest;

        await test.step('Send PUT request with minimal data', async () => {
            response = await applicationRequestsAPI.updateApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify response based on application state', async () => {
            if (response.status() === 200) {
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
            } else if (response.status() === 400) {
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors[0].message).toContain('can not update request');
            }
        });
    });

    test('Verify update application request API with non-existent ID', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestData = testData.updateRequest.invalidData.notFound as UpdateApplicationRequest & { expectedStatus: number; expectedMessage: string };

        await test.step('Send PUT request with non-existent ID', async () => {
            response = await applicationRequestsAPI.updateApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify not found error response', async () => {
            expect(response.status()).toBe(requestData.expectedStatus);
            expect(responseBody.statusCode).toBe(requestData.expectedStatus);
            expect(responseBody.isSuccess).toBe(false);
            expect(responseBody.errors).toBeDefined();
            expect(responseBody.errors.length).toBeGreaterThan(0);
        });
    });

    // Negative test cases for validation
    Object.entries(testData.updateRequest.invalidData).forEach(([testCase, invalidData]) => {
        if (testCase !== 'notFound') { // Skip notFound as it's tested separately
            test(`Verify update application request API validation - ${testCase}`, async () => {
                let response: any;
                let responseBody: ApiResponseType;
                const requestData = invalidData as UpdateApplicationRequest & { expectedStatus: number; expectedMessage: string };

                // Use current user's application ID if available, otherwise use test data ID
                if (currentUserApplicationId && testCase !== 'emptyId' && testCase !== 'invalidId') {
                    requestData.Id = currentUserApplicationId;
                }

                await test.step(`Send PUT request with ${testCase}`, async () => {
                    response = await applicationRequestsAPI.updateApplicationRequest(requestData);
                    responseBody = await response.json();
                });

                await test.step('Verify validation error response', async () => {
                    expect(response.status()).toBe(requestData.expectedStatus);
                    expect(responseBody.statusCode).toBe(requestData.expectedStatus);
                    expect(responseBody.isSuccess).toBe(false);

                    // Check if error message is present
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0]).toHaveProperty('message');
                        expect(typeof responseBody.errors[0].message).toBe('string');
                    }
                });
            });
        }
    });

    test('Verify update application request API response structure', async () => {
        let response: any;
        let responseBody: ApiResponseType;

        await test.step('Prepare request data', async () => {
            if (!currentUserApplicationId) {
                test.skip();
            }
        });

        const requestData: UpdateApplicationRequest = {
            ...testData.updateRequest.validData,
            Id: currentUserApplicationId
        } as UpdateApplicationRequest;

        await test.step('Send PUT request to update application request', async () => {
            response = await applicationRequestsAPI.updateApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify API response structure', async () => {
            // Verify standard API response structure
            expect(responseBody).toHaveProperty('statusCode');
            expect(responseBody).toHaveProperty('isSuccess');
            expect(responseBody).toHaveProperty('errors');

            expect(typeof responseBody.statusCode).toBe('number');
            expect(typeof responseBody.isSuccess).toBe('boolean');
            expect(Array.isArray(responseBody.errors)).toBe(true);

            // Data field is only present on successful responses
            if (response.status() === 200) {
                expect(responseBody).toHaveProperty('data');
            }
        });
    });

    test('Verify update application request API - business rule validation', async () => {
        let response: any;
        let responseBody: ApiResponseType;

        await test.step('Prepare request data', async () => {
            if (!currentUserApplicationId) {
                test.skip();
            }
        });

        const requestData: UpdateApplicationRequest = {
            ...testData.updateRequest.validData,
            Id: currentUserApplicationId
        } as UpdateApplicationRequest;

        await test.step('Send PUT request to update application request', async () => {
            response = await applicationRequestsAPI.updateApplicationRequest(requestData);
            responseBody = await response.json();
        });

        await test.step('Verify business rule enforcement', async () => {
            if (response.status() === 200) {
                console.log('✓ Application is in updatable state (under review)');
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
            } else if (response.status() === 400) {
                console.log('✓ API correctly enforces update restrictions based on application status');
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors).toBeDefined();
                expect(responseBody.errors.length).toBeGreaterThan(0);

                // Check for specific business rule error message
                const errorMessage = responseBody.errors[0].message;
                expect(errorMessage).toMatch(/can not update request|not under review/i);
            }
        });
    });
});

test.describe('Application Requests API - Get Requests (Pagination & Filtering) - Admin Access', () => {
    let applicationRequestsAPI: ApplicationRequestsAPI;

    test.beforeEach(async ({ playwright }) => {
        // Create admin authenticated request context
        const tempRequest = await playwright.request.newContext();
        const adminLoginAPI = new LoginLogoutAPI(tempRequest);

        const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
        const adminPassword = process.env.ADMIN_PASSWORD || 'admin123A@';

        const loginRes = await adminLoginAPI.login(adminEmail, adminPassword);
        const loginBody = await loginRes.json();

        if (loginRes.status() !== 200 || !loginBody.data?.accessToken) {
            throw new Error(`Admin login failed: ${loginBody.errors?.[0]?.message || 'Unknown error'}`);
        }

        const adminToken = loginBody.data.accessToken;
        const adminRequest = await playwright.request.newContext({
            extraHTTPHeaders: {
                Authorization: `Bearer ${adminToken}`,
            },
        });

        applicationRequestsAPI = new ApplicationRequestsAPI(adminRequest);

        // Clean up temp request
        await tempRequest.dispose();
    });

    test('Verify get application requests API with basic pagination', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams = testData.getRequests.validParams.basic as GetApplicationRequestsParams;

        await test.step('Send GET request with basic pagination parameters', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify successful response', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);
        });

        await test.step('Verify response data structure', async () => {
            expect(responseBody).toHaveProperty('data');

            if (responseBody.data) {
                // Verify pagination structure
                expect(responseBody.data).toHaveProperty('pageSize');
                expect(responseBody.data).toHaveProperty('pageNumber');
                expect(responseBody.data).toHaveProperty('totalCount');
                expect(responseBody.data).toHaveProperty('items');

                // Verify pagination data types
                expect(typeof responseBody.data.pageSize).toBe('number');
                expect(typeof responseBody.data.pageNumber).toBe('number');
                expect(typeof responseBody.data.totalCount).toBe('number');
                expect(Array.isArray(responseBody.data.items)).toBe(true);

                // Verify pagination values match request
                expect(responseBody.data.pageSize).toBe(requestParams.PageSize);
                expect(responseBody.data.pageNumber).toBe(requestParams.PageNumber);

                // Verify each application request in items array
                if (responseBody.data.items.length > 0) {
                    responseBody.data.items.forEach((applicationRequest: ApplicationRequestResponse) => {
                        testData.getRequests.expectedResponseFields.forEach(field => {
                            expect(applicationRequest).toHaveProperty(field);
                        });

                        // Verify data types
                        expect(typeof applicationRequest.id).toBe('string');
                        expect(typeof applicationRequest.education).toBe('string');
                        expect(typeof applicationRequest.workExperience).toBe('string');
                        expect(typeof applicationRequest.fullName).toBe('string');
                        expect(typeof applicationRequest.description).toBe('string');
                        expect(typeof applicationRequest.status).toBe('number');
                        expect(typeof applicationRequest.summitted).toBe('string');

                        // Verify status is a valid number
                        expect(applicationRequest.status).toBeGreaterThanOrEqual(0);
                        expect(applicationRequest.status).toBeLessThanOrEqual(4);
                    });

                    console.log(`✓ Retrieved ${responseBody.data.items.length} application requests out of ${responseBody.data.totalCount} total`);
                }
            }
        });
    });

    test('Verify get application requests API with search filter', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams = testData.getRequests.validParams.withSearch as GetApplicationRequestsParams;

        await test.step('Send GET request with search parameter', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify successful response with search filter', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);

            // Verify search functionality (if data exists)
            if (responseBody.data && responseBody.data.items && responseBody.data.items.length > 0) {
                // Check if search term appears in relevant fields
                const searchTerm = requestParams.Search?.toLowerCase();
                responseBody.data.items.forEach((item: ApplicationRequestResponse) => {
                    const searchableText = `${item.education} ${item.workExperience} ${item.fullName} ${item.description}`.toLowerCase();
                    // Note: This is a basic check - actual search implementation may vary
                    console.log(`Search term: ${searchTerm}, found in: ${searchableText.includes(searchTerm || '')}`);
                });

                console.log(`✓ Search returned ${responseBody.data.items.length} results for term: "${searchTerm}"`);
            } else {
                console.log(`✓ Search returned no results for term: "${requestParams.Search}"`);
            }
        });
    });

    test('Verify get application requests API with status filter', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams = testData.getRequests.validParams.withStatusFilter as GetApplicationRequestsParams;

        await test.step('Send GET request with status filter', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify successful response with status filter', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);

            // Verify status filtering (if data exists)
            if (responseBody.data && responseBody.data.items && responseBody.data.items.length > 0) {
                const allowedStatuses = requestParams.ApplicationRequestStatuses || [];
                responseBody.data.items.forEach((item: ApplicationRequestResponse) => {
                    expect(allowedStatuses).toContain(item.status);
                });

                console.log(`✓ Status filter returned ${responseBody.data.items.length} results for statuses: [${allowedStatuses.join(', ')}]`);
            } else {
                console.log(`✓ Status filter returned no results for statuses: [${requestParams.ApplicationRequestStatuses?.join(', ')}]`);
            }
        });
    });

    test('Verify get application requests API with all filters', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams = testData.getRequests.validParams.withAllFilters as GetApplicationRequestsParams;

        await test.step('Send GET request with all filters', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify successful response with all filters', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);

            // Verify combined filtering (if data exists)
            if (responseBody.data && Array.isArray(responseBody.data)) {
                expect(responseBody.data.length).toBeLessThanOrEqual(requestParams.PageSize);

                if (responseBody.data.length > 0) {
                    const allowedStatuses = requestParams.ApplicationRequestStatuses || [];
                    responseBody.data.forEach((item: ApplicationRequestResponse) => {
                        expect(allowedStatuses).toContain(item.status);
                    });
                }
            }
        });
    });

    test('Verify get application requests API with large page size', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams = testData.getRequests.validParams.largePageSize as GetApplicationRequestsParams;

        await test.step('Send GET request with large page size', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify successful response with large page size', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);

            // Verify page size limit is respected
            if (responseBody.data && Array.isArray(responseBody.data)) {
                expect(responseBody.data.length).toBeLessThanOrEqual(requestParams.PageSize);
            }
        });
    });

    test('Verify get application requests API with specific page number', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams = testData.getRequests.validParams.specificPage as GetApplicationRequestsParams;

        await test.step('Send GET request with specific page number', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify successful response for specific page', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);

            // Verify pagination works for specific page
            if (responseBody.data && Array.isArray(responseBody.data)) {
                expect(responseBody.data.length).toBeLessThanOrEqual(requestParams.PageSize);
            }
        });
    });

    // Negative test cases for invalid parameters
    Object.entries(testData.getRequests.invalidParams).forEach(([testCase, invalidParams]) => {
        test(`Verify get application requests API validation - ${testCase}`, async () => {
            let response: any;
            let responseBody: ApiResponseType;
            const requestParams = invalidParams as GetApplicationRequestsParams & { expectedStatus: number; expectedMessage: string };

            await test.step(`Send GET request with ${testCase}`, async () => {
                response = await applicationRequestsAPI.getApplicationRequests(requestParams);
                responseBody = await response.json();
            });

            await test.step('Verify validation error response', async () => {
                expect(response.status()).toBe(requestParams.expectedStatus);
                expect(responseBody.statusCode).toBe(requestParams.expectedStatus);
                expect(responseBody.isSuccess).toBe(false);

                // Check if error message is present
                if (responseBody.errors && responseBody.errors.length > 0) {
                    expect(responseBody.errors[0]).toHaveProperty('message');
                    expect(typeof responseBody.errors[0].message).toBe('string');
                }
            });
        });
    });

    test('Verify get application requests API response structure', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams = testData.getRequests.validParams.basic as GetApplicationRequestsParams;

        await test.step('Send GET request to get application requests', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify API response structure', async () => {
            // Verify standard API response structure
            expect(responseBody).toHaveProperty('statusCode');
            expect(responseBody).toHaveProperty('isSuccess');
            expect(responseBody).toHaveProperty('data');
            expect(responseBody).toHaveProperty('errors');

            expect(typeof responseBody.statusCode).toBe('number');
            expect(typeof responseBody.isSuccess).toBe('boolean');
            expect(Array.isArray(responseBody.errors)).toBe(true);

            // Data should be an array for list endpoints
            if (responseBody.data) {
                expect(Array.isArray(responseBody.data)).toBe(true);
            }
        });
    });

    test('Verify get application requests API - empty results handling', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams: GetApplicationRequestsParams = {
            PageSize: 10,
            PageNumber: 999, // Very high page number to likely get empty results
            Search: "nonexistentsearchterm12345"
        };

        await test.step('Send GET request with parameters likely to return empty results', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify successful response with empty results', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);

            // Verify empty results are handled properly
            if (responseBody.data) {
                expect(Array.isArray(responseBody.data)).toBe(true);
                // Empty results should return empty array, not null
                expect(responseBody.data).toEqual([]);
            }
        });
    });

    test('Verify get application requests API - status filtering validation', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const requestParams: GetApplicationRequestsParams = {
            PageSize: 10,
            PageNumber: 1,
            ApplicationRequestStatuses: [1, 2, 3, 4] // All possible statuses
        };

        await test.step('Send GET request with all status filters', async () => {
            response = await applicationRequestsAPI.getApplicationRequests(requestParams);
            responseBody = await response.json();
        });

        await test.step('Verify successful response with all status filters', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);

            // Verify all returned items have valid statuses
            if (responseBody.data && Array.isArray(responseBody.data) && responseBody.data.length > 0) {
                const validStatuses = Object.values(testData.getRequests.expectedStatuses);
                responseBody.data.forEach((item: ApplicationRequestResponse) => {
                    expect(validStatuses).toContain(item.status);
                });
            }
        });
    });
});

test.describe('Application Requests API - Get Request by ID - Admin Access', () => {
    let applicationRequestsAPI: ApplicationRequestsAPI;

    test.beforeEach(async ({ playwright }) => {
        // Create admin authenticated request context
        const tempRequest = await playwright.request.newContext();
        const adminLoginAPI = new LoginLogoutAPI(tempRequest);

        const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
        const adminPassword = process.env.ADMIN_PASSWORD || 'admin123A@';

        const loginRes = await adminLoginAPI.login(adminEmail, adminPassword);
        const loginBody = await loginRes.json();

        if (loginRes.status() !== 200 || !loginBody.data?.accessToken) {
            throw new Error(`Admin login failed: ${loginBody.errors?.[0]?.message || 'Unknown error'}`);
        }

        const adminToken = loginBody.data.accessToken;
        const adminRequest = await playwright.request.newContext({
            extraHTTPHeaders: {
                Authorization: `Bearer ${adminToken}`,
            },
        });

        applicationRequestsAPI = new ApplicationRequestsAPI(adminRequest);

        // Clean up temp request
        await tempRequest.dispose();

        // Note: For admin access, we'll use known IDs from the API response instead of current user ID
        // since admin can access any application request
    });

    test('Verify get application request by ID API with valid ID successfully', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const knownValidId = "a74b2365-14dd-4c18-f003-08dd9dcaf40f"; // ID from actual API response

        await test.step('Send GET request with valid application ID', async () => {
            response = await applicationRequestsAPI.getApplicationRequestById(knownValidId);
            responseBody = await response.json();
        });

        await test.step('Verify successful response', async () => {
            expect(response.status()).toBe(200);
            expect(responseBody.statusCode).toBe(200);
            expect(responseBody.isSuccess).toBe(true);
        });

        await test.step('Verify detailed response data structure', async () => {
            expect(responseBody).toHaveProperty('data');

            if (responseBody.data) {
                const applicationDetail = responseBody.data as ApplicationRequestDetailResponse;

                // Verify all expected fields are present
                testData.getRequestById.expectedDetailFields.forEach(field => {
                    expect(applicationDetail).toHaveProperty(field);
                });

                // Verify data types for basic fields
                expect(typeof applicationDetail.id).toBe('string');
                expect(typeof applicationDetail.education).toBe('string');
                expect(typeof applicationDetail.workExperience).toBe('string');
                expect(typeof applicationDetail.fullName).toBe('string');
                expect(typeof applicationDetail.description).toBe('string');
                expect(typeof applicationDetail.status).toBe('number');
                expect(typeof applicationDetail.summitted).toBe('string');

                // Verify mentor-specific fields
                expect(typeof applicationDetail.mentorEmail).toBe('string');
                expect(Array.isArray(applicationDetail.mentorCertifications)).toBe(true);
                expect(typeof applicationDetail.avatarUrl).toBe('string');
                expect(Array.isArray(applicationDetail.mentorExpertises)).toBe(true);
                expect(Array.isArray(applicationDetail.applicationRequestDocuments)).toBe(true);

                // Verify mentor expertises are strings
                if (applicationDetail.mentorExpertises.length > 0) {
                    applicationDetail.mentorExpertises.forEach(expertise => {
                        expect(typeof expertise).toBe('string');
                    });
                }

                // Verify mentor certifications are strings
                if (applicationDetail.mentorCertifications.length > 0) {
                    applicationDetail.mentorCertifications.forEach(certification => {
                        expect(typeof certification).toBe('string');
                    });
                }

                // Verify note field (can be string or null)
                expect(['string', 'object']).toContain(typeof applicationDetail.note);

                // Verify document structure if documents exist
                if (applicationDetail.applicationRequestDocuments.length > 0) {
                    applicationDetail.applicationRequestDocuments.forEach(doc => {
                        testData.getRequestById.expectedDocumentFields.forEach(field => {
                            expect(doc).toHaveProperty(field);
                        });
                        expect(typeof doc.filePath).toBe('string');
                        expect(typeof doc.fileName).toBe('string');
                    });
                }

                // Verify the ID matches the requested ID
                expect(applicationDetail.id).toBe(knownValidId);
            }
        });
    });

    test('Verify get application request by ID API with non-existent ID', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const nonExistentId = testData.getRequestById.notFoundId.id;

        await test.step('Send GET request with non-existent ID', async () => {
            response = await applicationRequestsAPI.getApplicationRequestById(nonExistentId);
            responseBody = await response.json();
        });

        await test.step('Verify not found error response', async () => {
            expect(response.status()).toBe(testData.getRequestById.notFoundId.expectedStatus);
            expect(responseBody.statusCode).toBe(testData.getRequestById.notFoundId.expectedStatus);
            expect(responseBody.isSuccess).toBe(false);
            expect(responseBody.errors).toBeDefined();
            expect(responseBody.errors.length).toBeGreaterThan(0);
        });
    });

    // Test invalid ID formats
    testData.getRequestById.invalidIdFormats.forEach((invalidIdData) => {
        test(`Verify get application request by ID API validation - ${invalidIdData.id || 'empty ID'}`, async () => {
            let response: any;
            let responseBody: ApiResponseType;

            await test.step(`Send GET request with invalid ID: ${invalidIdData.id || 'empty'}`, async () => {
                response = await applicationRequestsAPI.getApplicationRequestById(invalidIdData.id);
                responseBody = await response.json();
            });

            await test.step('Verify validation error response', async () => {
                expect(response.status()).toBe(invalidIdData.expectedStatus);
                expect(responseBody.statusCode).toBe(invalidIdData.expectedStatus);
                expect(responseBody.isSuccess).toBe(false);

                // Check if error message is present
                if (responseBody.errors && responseBody.errors.length > 0) {
                    expect(responseBody.errors[0]).toHaveProperty('message');
                    expect(typeof responseBody.errors[0].message).toBe('string');
                }
            });
        });
    });

    test('Verify get application request by ID API response structure', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const knownValidId = "a74b2365-14dd-4c18-f003-08dd9dcaf40f"; // ID from actual API response

        await test.step('Send GET request with valid application ID', async () => {
            response = await applicationRequestsAPI.getApplicationRequestById(knownValidId);
            responseBody = await response.json();
        });

        await test.step('Verify API response structure', async () => {
            // Verify standard API response structure
            expect(responseBody).toHaveProperty('statusCode');
            expect(responseBody).toHaveProperty('isSuccess');
            expect(responseBody).toHaveProperty('data');
            expect(responseBody).toHaveProperty('errors');

            expect(typeof responseBody.statusCode).toBe('number');
            expect(typeof responseBody.isSuccess).toBe('boolean');
            expect(Array.isArray(responseBody.errors)).toBe(true);

            // Data field should contain detailed application information
            if (response.status() === 200) {
                expect(responseBody.data).toBeDefined();
                expect(typeof responseBody.data).toBe('object');
            }
        });
    });

    test('Verify get application request by ID API - mentor information validation', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const knownValidId = "a74b2365-14dd-4c18-f003-08dd9dcaf40f"; // ID from actual API response

        await test.step('Send GET request with valid application ID', async () => {
            response = await applicationRequestsAPI.getApplicationRequestById(knownValidId);
            responseBody = await response.json();
        });

        await test.step('Verify mentor-specific information', async () => {
            if (response.status() === 200 && responseBody.data) {
                const applicationDetail = responseBody.data as ApplicationRequestDetailResponse;

                // Verify mentor email format (basic validation)
                if (applicationDetail.mentorEmail) {
                    expect(applicationDetail.mentorEmail).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
                }

                // Verify mentor expertises are strings
                if (applicationDetail.mentorExpertises && applicationDetail.mentorExpertises.length > 0) {
                    applicationDetail.mentorExpertises.forEach(expertise => {
                        expect(typeof expertise).toBe('string');
                        expect(expertise.length).toBeGreaterThan(0);
                    });
                    console.log(`✓ Mentor expertises: [${applicationDetail.mentorExpertises.join(', ')}]`);
                }

                // Verify mentor certifications are strings
                if (applicationDetail.mentorCertifications && applicationDetail.mentorCertifications.length > 0) {
                    applicationDetail.mentorCertifications.forEach(certification => {
                        expect(typeof certification).toBe('string');
                        expect(certification.length).toBeGreaterThan(0);
                    });
                    console.log(`✓ Mentor certifications: [${applicationDetail.mentorCertifications.join(', ')}]`);
                }

                // Verify avatar URL format (if present and not empty)
                if (applicationDetail.avatarUrl && applicationDetail.avatarUrl.length > 0) {
                    expect(typeof applicationDetail.avatarUrl).toBe('string');
                    // Basic URL validation
                    expect(applicationDetail.avatarUrl).toMatch(/^https?:\/\/.+/);
                    console.log(`✓ Avatar URL: ${applicationDetail.avatarUrl}`);
                } else {
                    console.log('✓ No avatar URL provided');
                }

                console.log('✓ Mentor information validation passed');
            }
        });
    });

    test('Verify get application request by ID API - document information validation', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const knownValidId = "a74b2365-14dd-4c18-f003-08dd9dcaf40f"; // ID from actual API response

        await test.step('Send GET request with valid application ID', async () => {
            response = await applicationRequestsAPI.getApplicationRequestById(knownValidId);
            responseBody = await response.json();
        });

        await test.step('Verify document information structure', async () => {
            if (response.status() === 200 && responseBody.data) {
                const applicationDetail = responseBody.data as ApplicationRequestDetailResponse;

                // Verify documents array structure
                expect(Array.isArray(applicationDetail.applicationRequestDocuments)).toBe(true);

                // If documents exist, verify their structure
                if (applicationDetail.applicationRequestDocuments.length > 0) {
                    applicationDetail.applicationRequestDocuments.forEach((doc, index) => {
                        expect(doc).toHaveProperty('filePath');
                        expect(doc).toHaveProperty('fileName');
                        expect(typeof doc.filePath).toBe('string');
                        expect(typeof doc.fileName).toBe('string');
                        expect(doc.filePath.length).toBeGreaterThan(0);
                        expect(doc.fileName.length).toBeGreaterThan(0);

                        console.log(`✓ Document ${index + 1}: ${doc.fileName} at ${doc.filePath}`);
                    });
                } else {
                    console.log('✓ No documents attached to this application');
                }
            }
        });
    });

    test('Verify get application request by ID API with known valid ID from API response', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const knownValidId = "a74b2365-14dd-4c18-f003-08dd9dcaf40f"; // ID from actual API response

        await test.step('Send GET request with known valid application ID', async () => {
            response = await applicationRequestsAPI.getApplicationRequestById(knownValidId);
            responseBody = await response.json();
        });

        await test.step('Verify successful response with known ID', async () => {
            if (response.status() === 200) {
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
                expect(responseBody.data).toBeDefined();

                const applicationDetail = responseBody.data as ApplicationRequestDetailResponse;

                // Verify the response matches the expected structure from API documentation
                expect(applicationDetail.id).toBe(knownValidId);
                expect(typeof applicationDetail.note).toBe('string');
                expect(Array.isArray(applicationDetail.applicationRequestDocuments)).toBe(true);
                expect(typeof applicationDetail.mentorEmail).toBe('string');
                expect(Array.isArray(applicationDetail.mentorExpertises)).toBe(true);
                expect(Array.isArray(applicationDetail.mentorCertifications)).toBe(true);
                expect(typeof applicationDetail.avatarUrl).toBe('string');

                // Verify document structure if documents exist
                if (applicationDetail.applicationRequestDocuments.length > 0) {
                    applicationDetail.applicationRequestDocuments.forEach(doc => {
                        expect(doc).toHaveProperty('filePath');
                        expect(doc).toHaveProperty('fileName');
                        expect(typeof doc.filePath).toBe('string');
                        expect(typeof doc.fileName).toBe('string');

                        // Verify filePath is a valid URL (based on actual response)
                        if (doc.filePath.startsWith('http')) {
                            expect(doc.filePath).toMatch(/^https?:\/\/.+/);
                        }
                    });
                }

                console.log(`✓ Successfully retrieved application details for ID: ${knownValidId}`);
                console.log(`✓ Mentor: ${applicationDetail.fullName} (${applicationDetail.mentorEmail})`);
                console.log(`✓ Status: ${applicationDetail.status}, Note: "${applicationDetail.note}"`);
                console.log(`✓ Documents: ${applicationDetail.applicationRequestDocuments.length} attached`);
                console.log(`✓ Expertises: [${applicationDetail.mentorExpertises.join(', ')}]`);
                console.log(`✓ Certifications: [${applicationDetail.mentorCertifications.join(', ')}]`);
            } else if (response.status() === 404) {
                console.log(`ℹ Application with ID ${knownValidId} no longer exists (404)`);
                expect(responseBody.statusCode).toBe(404);
                expect(responseBody.isSuccess).toBe(false);
            } else {
                console.log(`ℹ Unexpected response status: ${response.status()}`);
                expect(response.status()).toBeGreaterThanOrEqual(200);
            }
        });
    });
});

test.describe('Application Requests API - Reject Request - Admin Access', () => {
    let applicationRequestsAPI: ApplicationRequestsAPI;

    test.beforeEach(async ({ playwright }) => {
        // Create admin authenticated request context
        const tempRequest = await playwright.request.newContext();
        const adminLoginAPI = new LoginLogoutAPI(tempRequest);

        const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
        const adminPassword = process.env.ADMIN_PASSWORD || 'admin123A@';

        const loginRes = await adminLoginAPI.login(adminEmail, adminPassword);
        const loginBody = await loginRes.json();

        if (loginRes.status() !== 200 || !loginBody.data?.accessToken) {
            throw new Error(`Admin login failed: ${loginBody.errors?.[0]?.message || 'Unknown error'}`);
        }

        const adminToken = loginBody.data.accessToken;
        const adminRequest = await playwright.request.newContext({
            extraHTTPHeaders: {
                Authorization: `Bearer ${adminToken}`,
            },
        });

        applicationRequestsAPI = new ApplicationRequestsAPI(adminRequest);

        // Clean up temp request
        await tempRequest.dispose();
    });

    test('Verify reject application request API with valid data successfully', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const applicationId = testData.rejectRequest.testIds.validForRejection;
        const rejectData = testData.rejectRequest.validData as RejectRequestBody;

        await test.step('Send PUT request to reject application request', async () => {
            response = await applicationRequestsAPI.rejectRequest(applicationId, rejectData);
            responseBody = await response.json();
        });

        await test.step('Verify successful rejection response', async () => {
            if (response.status() === 200) {
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
                expect(Array.isArray(responseBody.errors)).toBe(true);
                expect(responseBody.errors.length).toBe(0);

                console.log(`✓ Successfully rejected application ${applicationId}`);
                console.log(`✓ Rejection note: "${rejectData.note}"`);
            } else if (response.status() === 400) {
                // Application might already be rejected or in wrong state
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors).toBeDefined();
                expect(responseBody.errors.length).toBeGreaterThan(0);

                console.log(`ℹ Application cannot be rejected: ${responseBody.errors[0]?.message}`);
            } else if (response.status() === 404) {
                // Application not found
                expect(responseBody.statusCode).toBe(404);
                expect(responseBody.isSuccess).toBe(false);

                console.log(`ℹ Application ${applicationId} not found`);
            }
        });
    });

    test('Verify reject application request API with detailed note', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const applicationId = testData.rejectRequest.testIds.validForRejection;
        const rejectData = testData.rejectRequest.validDataDetailed as RejectRequestBody;

        await test.step('Send PUT request with detailed rejection note', async () => {
            response = await applicationRequestsAPI.rejectRequest(applicationId, rejectData);
            responseBody = await response.json();
        });

        await test.step('Verify response with detailed note', async () => {
            if (response.status() === 200) {
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
                expect(responseBody.errors.length).toBe(0);

                console.log(`✓ Successfully rejected application with detailed note`);
            } else {
                // Handle cases where application is already rejected or in wrong state
                console.log(`ℹ Application rejection response: ${response.status()}`);
                expect([200, 400, 404]).toContain(response.status());
            }
        });
    });

    test('Verify reject application request API with short note', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const applicationId = testData.rejectRequest.testIds.validForRejection;
        const rejectData = testData.rejectRequest.validDataShort as RejectRequestBody;

        await test.step('Send PUT request with short rejection note', async () => {
            response = await applicationRequestsAPI.rejectRequest(applicationId, rejectData);
            responseBody = await response.json();
        });

        await test.step('Verify response with short note', async () => {
            if (response.status() === 200) {
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
                expect(responseBody.errors.length).toBe(0);

                console.log(`✓ Successfully rejected application with short note`);
            } else {
                console.log(`ℹ Application rejection response: ${response.status()}`);
                expect([200, 400, 404]).toContain(response.status());
            }
        });
    });

    test('Verify reject application request API with non-existent ID', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const nonExistentId = testData.rejectRequest.businessRuleErrors.notFound.id;
        const rejectData = { note: testData.rejectRequest.businessRuleErrors.notFound.note } as RejectRequestBody;

        await test.step('Send PUT request with non-existent application ID', async () => {
            response = await applicationRequestsAPI.rejectRequest(nonExistentId, rejectData);
            responseBody = await response.json();
        });

        await test.step('Verify not found error response', async () => {
            expect(response.status()).toBe(testData.rejectRequest.businessRuleErrors.notFound.expectedStatus);
            expect(responseBody.statusCode).toBe(testData.rejectRequest.businessRuleErrors.notFound.expectedStatus);
            expect(responseBody.isSuccess).toBe(false);
            expect(responseBody.errors).toBeDefined();
            expect(responseBody.errors.length).toBeGreaterThan(0);

            console.log(`✓ Correctly handled non-existent application ID: ${nonExistentId}`);
        });
    });

    // Negative test cases for validation
    Object.entries(testData.rejectRequest.invalidData).forEach(([testCase, invalidData]) => {
        test(`Verify reject application request API validation - ${testCase}`, async () => {
            let response: any;
            let responseBody: ApiResponseType;
            const applicationId = testData.rejectRequest.testIds.validForRejection;
            const rejectData = invalidData as RejectRequestBody & { expectedStatus: number; expectedMessage: string };

            await test.step(`Send PUT request with ${testCase}`, async () => {
                response = await applicationRequestsAPI.rejectRequest(applicationId, rejectData);
                responseBody = await response.json();
            });

            await test.step('Verify validation error response', async () => {
                expect(response.status()).toBe(rejectData.expectedStatus);
                expect(responseBody.statusCode).toBe(rejectData.expectedStatus);
                expect(responseBody.isSuccess).toBe(false);

                // Check if error message is present
                if (responseBody.errors && responseBody.errors.length > 0) {
                    expect(responseBody.errors[0]).toHaveProperty('message');
                    expect(typeof responseBody.errors[0].message).toBe('string');
                }

                console.log(`✓ Validation error for ${testCase}: ${responseBody.errors[0]?.message}`);
            });
        });
    });

    test('Verify reject application request API response structure', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const applicationId = testData.rejectRequest.testIds.validForRejection;
        const rejectData = testData.rejectRequest.validData as RejectRequestBody;

        await test.step('Send PUT request to reject application request', async () => {
            response = await applicationRequestsAPI.rejectRequest(applicationId, rejectData);
            responseBody = await response.json();
        });

        await test.step('Verify API response structure', async () => {
            // Verify standard API response structure
            expect(responseBody).toHaveProperty('statusCode');
            expect(responseBody).toHaveProperty('isSuccess');
            expect(responseBody).toHaveProperty('errors');

            expect(typeof responseBody.statusCode).toBe('number');
            expect(typeof responseBody.isSuccess).toBe('boolean');
            expect(Array.isArray(responseBody.errors)).toBe(true);

            // For rejection endpoint, successful response typically doesn't include data field
            if (response.status() === 200) {
                expect(responseBody.errors.length).toBe(0);
            }

            console.log(`✓ Response structure validation passed for status: ${response.status()}`);
        });
    });

    test('Verify reject application request API - business rule enforcement', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const underReviewId = testData.rejectRequest.testIds.underReviewId;
        const rejectData = { note: testData.rejectRequest.businessRuleErrors.underReview.note } as RejectRequestBody;

        await test.step('Send PUT request to reject application under review', async () => {
            response = await applicationRequestsAPI.rejectRequest(underReviewId, rejectData);
            responseBody = await response.json();
        });

        await test.step('Verify business rule enforcement', async () => {
            if (response.status() === 200) {
                console.log('✓ Application was successfully rejected (not under review)');
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
            } else if (response.status() === 400) {
                console.log('✓ API correctly enforces business rule - cannot reject under review application');
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors).toBeDefined();
                expect(responseBody.errors.length).toBeGreaterThan(0);

                // Check for specific business rule error message
                const errorMessage = responseBody.errors[0].message;
                expect(errorMessage).toMatch(/cannot reject.*under review/i);
            } else {
                console.log(`ℹ Unexpected response status: ${response.status()}`);
                expect([200, 400, 404]).toContain(response.status());
            }
        });
    });

    test('Verify reject application request API - already approved application', async () => {
        let response: any;
        let responseBody: ApiResponseType;
        const approvedId = testData.rejectRequest.testIds.approvedId;
        const rejectData = { note: testData.rejectRequest.businessRuleErrors.alreadyApproved.note } as RejectRequestBody;

        await test.step('Send PUT request to reject already approved application', async () => {
            response = await applicationRequestsAPI.rejectRequest(approvedId, rejectData);
            responseBody = await response.json();
        });

        await test.step('Verify approved application rejection handling', async () => {
            if (response.status() === 200) {
                console.log('✓ Application was successfully rejected (not approved or status changed)');
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBe(true);
            } else if (response.status() === 400) {
                console.log('✓ API correctly prevents rejection of approved application');
                expect(responseBody.statusCode).toBe(400);
                expect(responseBody.isSuccess).toBe(false);
                expect(responseBody.errors).toBeDefined();
                expect(responseBody.errors.length).toBeGreaterThan(0);

                // Check for specific business rule error message
                const errorMessage = responseBody.errors[0].message;
                expect(errorMessage).toMatch(/cannot reject.*approved/i);
            } else {
                console.log(`ℹ Unexpected response status: ${response.status()}`);
                expect([200, 400, 404]).toContain(response.status());
            }
        });
    });
});